import uuid
from datetime import datetime
from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPEx<PERSON>
from cortexacommon.logging import get_logger

from src.core.context import ApplicationContext, get_app_context
from src.models.call import Call, CallStatus
from src.schemas.call import CallResponse

router = APIRouter()
logger = get_logger(__name__)


@router.post("/", response_model=CallResponse, status_code=201)
async def create_call(
    app_context: ApplicationContext = Depends(get_app_context)
):
    """
    Create a new call. This is the initial entry point for a call.
    It creates a record in the database and a corresponding media session
    in the voice-router.
    """
    # 1. Create a new call record in the database
    new_call = Call()
    app_context.db_session.add(new_call)
    await app_context.db_session.commit()
    await app_context.db_session.refresh(new_call)

    # 2. Command the voice-router to create a media session
    try:
        media_session_id = await app_context.voice_router_service.create_media_session()
        new_call.media_session_id = media_session_id
        await app_context.db_session.commit()
    except Exception as e:
        # Rollback if voice-router communication fails
        await app_context.db_session.delete(new_call)
        await app_context.db_session.commit()
        raise HTTPException(status_code=502, detail=f"Voice router unavailable: {e}")

    return new_call

@router.post("/{call_id}/end", status_code=204)
async def end_call(
    call_id: uuid.UUID,
    app_context: ApplicationContext = Depends(get_app_context)
):
    """End a call, terminating the media session and updating its status."""
    db_call = await app_context.db_session.get(Call, call_id)
    if not db_call:
        raise HTTPException(status_code=404, detail="Call not found")

    if db_call.status == CallStatus.ENDED:
        return # Idempotent: already ended

    # Command the voice-router to terminate the media session
    if db_call.media_session_id:
        try:
            await app_context.voice_router_service.end_media_session(db_call.media_session_id)
        except Exception as e:
            # Log the error but proceed to mark the call as ended
            logger.exception(f"Could not end media session {db_call.media_session_id}: {e}")

    db_call.status = CallStatus.ENDED
    db_call.ended_at = datetime.utcnow()
    await app_context.db_session.commit()



# Note: Mute/Unmute would require tracking participant producer IDs,
# which adds significant complexity. For this design, we'll assume
# mute/unmute is a client-side action signaled directly to the voice-router,
# with the Call Controller's endpoint acting as a potential future hook.

# @router.post("/{call_id}/features/translation", status_code=201)
# async def enable_translation(
#     call_id: uuid.UUID,
#     app_context: ApplicationContext = Depends(get_app_context)
# ):
#     """Enable translation for a call by starting an RTP fork."""
#     db_call = await app_context.db_session.get(Call, call_id)
#     if not db_call or not db_call.media_session_id:
#         raise HTTPException(status_code=404, detail="Active call session not found")

#     if db_call.translation_fork_id:
#         raise HTTPException(status_code=409, detail="Translation is already enabled")

#     try:
#         fork_id = await app_context.voice_router_service.start_translation_fork(db_call.media_session_id)
#         db_call.translation_fork_id = fork_id
#         await app_context.db_session.commit()
#         return {"fork_id": fork_id}
#     except Exception as e:
#         raise HTTPException(status_code=502, detail=f"Could not enable translation fork: {e}")

# @router.delete("/{call_id}/features/translation", status_code=204)
# async def disable_translation(
#     call_id: uuid.UUID,
#     app_context: ApplicationContext = Depends(get_app_context)
# ):
#     """Disable translation for a call by stopping the RTP fork."""
#     db_call = await app_context.db_session.get(Call, call_id)
#     if not db_call or not db_call.media_session_id or not db_call.translation_fork_id:
#         raise HTTPException(status_code=404, detail="Translation feature not active for this call")

#     try:
#         await app_context.voice_router_service.stop_translation_fork(
#             db_call.media_session_id, db_call.translation_fork_id
#         )
#         db_call.translation_fork_id = None
#         await app_context.db_session.commit()
#     except Exception as e:
#         raise HTTPException(status_code=502, detail=f"Could not disable translation fork: {e}")