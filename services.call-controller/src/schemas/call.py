import uuid
from pydantic import BaseModel
from datetime import datetime
from..models.call import CallStatus


class CallBase(BaseModel):
    pass


class CallResponse(CallBase):
    """
    The response model for a call object.
    """
    id: uuid.UUID
    status: CallStatus
    created_at: datetime
    media_session_id: str | None = None

    class Config:
        from_attributes = True # Allows creating this schema from a SQLAlchemy model
