"""
Application context for managing shared resources in the voice-gateway service.

This module provides a centralized way to manage application-wide resources
like S2ST processor instances, ensuring proper initialization and cleanup.
"""

import asyncio
from fastapi import FastAPI
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.logging import get_logger

from ..services.voice_router import VoiceRouterService
from .database import AsyncSessionLocal

logger = get_logger(__name__)


class ApplicationContext:
    """
    Application context for managing shared resources.
    
    This class provides a singleton pattern for managing application-wide
    resources like S2ST processor instances.
    """
    
    _instance: 'ApplicationContext | None' = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        """Initialize the application context."""
        self._initialized = False
        self._db_session: AsyncSession | None = None
        self._voice_router_service: VoiceRouterService | None = None
    
    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        """
        Get the singleton instance of the application context.
        
        Returns:
            ApplicationContext: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @property
    def is_initialized(self) -> bool:
        """
        Check if the application context is initialized.
        
        Returns:
            bool: True if initialized, False otherwise
        """
        return self._initialized
    
    async def initialize(self, app: FastAPI) -> None:
        """
        Initialize the application context and its resources.
        
        This method initializes the S2ST processor and other shared resources.
        """
        if self._initialized:
            logger.warning("Application context already initialized")
            return

        logger.info("Initializing application context...")
        try:
            self._db_session = AsyncSessionLocal()
            self._voice_router_service = VoiceRouterService()
            
            self._initialized = True
            logger.info("Application context initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application context: {e}")
            self._initialized = False
            raise

    async def cleanup(self) -> None:
        """
        Clean up the application context and its resources.
        
        This method should be called during application shutdown.
        """
        logger.info("Cleaning up application context...")
        
        try:
            # Close the database session
            if self._db_session:
                await self._db_session.close()
                self._db_session = None

            self._initialized = False
            logger.info("Application context cleanup complete")
            
        except Exception as e:
            logger.error(f"Error during application context cleanup: {e}")
    
    @classmethod
    async def reset_instance(cls) -> None:
        """
        Reset the singleton instance (mainly for testing).
        
        This method should only be used in test scenarios.
        """
        async with cls._lock:
            if cls._instance:
                await cls._instance.cleanup()
            cls._instance = None

    @property
    def db_session(self) -> AsyncSession:
        """Get the database session."""
        if not self._db_session:
            raise RuntimeError("Database session not initialized")
        return self._db_session
    
    @property
    def voice_router_service(self) -> VoiceRouterService:
        """Get the voice router service."""
        if not self._voice_router_service:
            raise RuntimeError("Voice router service not initialized")
        return self._voice_router_service


# Global function to get the application context
async def get_app_context() -> ApplicationContext:
    """
    Get the application context instance.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await ApplicationContext.get_instance()
